import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

def display_dashboard():
    """Display interactive dashboard for backtest results."""
    root = tk.Tk()
    root.title("AdaptiveCapture Fund Dashboard")
    
    # ETF selection
    tk.Label(root, text="Select ETF:").grid(row=0, column=0, padx=5, pady=5)
    etf_var = tk.StringVar(value="SPY")
    etf_menu = ttk.Combobox(root, textvariable=etf_var, values=["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF"])
    etf_menu.grid(row=0, column=1, padx=5, pady=5)
    
    # Period selection
    tk.Label(root, text="Select Period:").grid(row=1, column=0, padx=5, pady=5)
    period_var = tk.StringVar(value="1990-2025")
    period_menu = ttk.Combobox(root, textvariable=period_var, values=["1990-2025", "2000-2025", "2010-2025", "2015-2025", "1yr", "3yr", "5yr"])
    period_menu.grid(row=1, column=1, padx=5, pady=5)
    
    # Start date entry
    tk.Label(root, text="Custom Start Date (YYYY-MM-DD):").grid(row=2, column=0, padx=5, pady=5)
    start_date_var = tk.StringVar(value="1990-01-01")
    start_date_entry = tk.Entry(root, textvariable=start_date_var)
    start_date_entry.grid(row=2, column=1, padx=5, pady=5)
    
    # End date entry
    tk.Label(root, text="Custom End Date (YYYY-MM-DD):").grid(row=3, column=0, padx=5, pady=5)
    end_date_var = tk.StringVar(value="2025-08-01")
    end_date_entry = tk.Entry(root, textvariable=end_date_var)
    end_date_entry.grid(row=3, column=1, padx=5, pady=5)
    
    # Metric selection
    tk.Label(root, text="Select Metric:").grid(row=4, column=0, padx=5, pady=5)
    metric_var = tk.StringVar(value="Cumulative")
    metric_menu = ttk.Combobox(root, textvariable=metric_var, values=["Cumulative", "Returns", "Volatility", "Sharpe", "Sortino", "Calmar", "Alpha vs JEPI", "Signal Flips", "VaR 95%", "CVaR 95%", "Signal Contribution"])
    metric_menu.grid(row=4, column=1, padx=5, pady=5)
    
    # Rebalance type selection
    tk.Label(root, text="Select Rebalance Type:").grid(row=5, column=0, padx=5, pady=5)
    rebalance_var = tk.StringVar(value="last_day")
    rebalance_menu = ttk.Combobox(root, textvariable=rebalance_var, values=["daily", "last_day", "3_consecutive", "5_consecutive"])
    rebalance_menu.grid(row=5, column=1, padx=5, pady=5)
    
    # PnL trigger selection
    tk.Label(root, text="Select PnL Trigger (%):").grid(row=6, column=0, padx=5, pady=5)
    pnl_var = tk.StringVar(value="5")
    pnl_menu = ttk.Combobox(root, textvariable=pnl_var, values=["5", "10", "25", "30"])
    pnl_menu.grid(row=6, column=1, padx=5, pady=5)
    
    # Quarter selection
    tk.Label(root, text="Select Quarter (YYYY-Q):").grid(row=7, column=0, padx=5, pady=5)
    quarter_var = tk.StringVar(value="All")
    quarter_menu = ttk.Combobox(root, textvariable=quarter_var, values=["All"] + [f"{y}-Q{q}" for y in range(1990, 2026) for q in range(1, 5)])
    quarter_menu.grid(row=7, column=1, padx=5, pady=5)
    
    def update_plot():
        """Update the dashboard plot based on user selections."""
        etf = etf_var.get()
        period = period_var.get()
        start_date = start_date_var.get()
        end_date = end_date_var.get()
        metric = metric_var.get()
        rebalance_type = rebalance_var.get()
        pnl_trigger = float(pnl_var.get()) / 100
        quarter = quarter_var.get()
        
        # Validate dates
        try:
            pd.to_datetime(start_date)
            pd.to_datetime(end_date)
        except ValueError:
            messagebox.showerror("Error", "Invalid date format. Use YYYY-MM-DD.")
            return
        
        # Set period dates
        start_dates = {"1990-2025": "1990-01-01", "2000-2025": "2000-01-01", "2010-2025": "2010-01-01", "2015-2025": "2015-08-01", "1yr": "2024-08-01", "3yr": "2022-08-01", "5yr": "2020-08-01"}
        end_dates = {"1990-2025": "2025-08-01", "2000-2025": "2025-08-01", "2010-2025": "2025-08-01", "2015-2025": "2025-08-01", "1yr": "2025-08-01", "3yr": "2025-08-01", "5yr": "2025-08-01"}
        if period != "Custom":
            start_date = start_dates.get(period, start_date)
            end_date = end_dates.get(period, end_date)
        
        fig, ax = plt.subplots(figsize=(8, 4))
        strategies = ["jepi_hold_last_day", f"{etf.lower()}_timing_vix18_{rebalance_type}_pnl{int(pnl_trigger*100) if pnl_trigger <= 0.15 else int(pnl_trigger*100)}"]
        for strategy in strategies:
            try:
                df = pd.read_csv(f"data/output/mcp_results_{strategy}.csv", parse_dates=["Date"])
                df = df[(df["Date"] >= start_date) & (df["Date"] <= end_date)]
                if metric in ["Cumulative", "Returns", "Volatility"]:
                    if metric == "Cumulative":
                        values = df["Fund_Cumulative"]
                        ax.set_ylabel("Cumulative Value ($)")
                    elif metric == "Returns":
                        values = df["Total_Return"] * 100
                        ax.set_ylabel("Monthly Return (%)")
                    elif metric == "Volatility":
                        values = df["Total_Return"].rolling(12).std() * np.sqrt(12) * 100
                        ax.set_ylabel("Annualized Volatility (%)")
                    ax.plot(df["Date"], values, label=strategy)
                elif metric == "Signal Contribution":
                    df_signals = pd.read_csv(f"data/output/signals_{etf}_{rebalance_type}_pnl{int(pnl_trigger*100) if pnl_trigger <= 0.15 else int(pnl_trigger*100)}.csv")
                    contributions = df_signals[["rsi_signal", "macd_signal", "bb_signal", "vix_signal", "ema_signal", "volume_signal", "wedge_signal", "voting_signal"]].mean()
                    sns.barplot(x=contributions.values, y=contributions.index, ax=ax)
                    ax.set_xlabel("Signal Contribution")
                    ax.set_title("Signal Contribution to Performance")
                else:
                    df_advanced = pd.read_csv(f"data/output/risk_stats_{etf}.csv")
                    row = df_advanced[(df_advanced["Ticker"] == etf) & (df_advanced["Trigger"] == rebalance_type) & (df_advanced["PnL Trigger"] == pnl_trigger)]
                    if not row.empty:
                        value = row[metric.split()[0] + " Ratio"].iloc[0] if metric not in ["Alpha vs JEPI", "Signal Flips", "VaR 95%", "CVaR 95%"] else row[metric.replace(" ", "_") + " (%)"].iloc[0]
                        if metric == "Signal Flips":
                            df_signals = pd.read_csv(f"data/output/signals_{etf}_{rebalance_type}_pnl{int(pnl_trigger*100) if pnl_trigger <= 0.15 else int(pnl_trigger*100)}.csv")
                            value = df_signals["signal_flips"].mean()
                        ax.bar(strategy, value, label=f"{strategy} ({metric})")
                        ax.set_ylabel(f"{metric} (%)" if metric in ["Alpha vs JEPI", "Signal Flips", "VaR 95%", "CVaR 95%"] else f"{metric} Ratio")
            except FileNotFoundError:
                messagebox.showerror("Error", f"Missing file for strategy: {strategy}")
                continue
        
        if quarter != "All":
            try:
                year, q = quarter.split("-Q")
                quarter_end = pd.to_datetime(f"{year}-{int(q)*3:02d}-01").strftime("%Y%m%d")
                df_quarter = pd.read_csv(f"data/output/quarterly_stats_{etf}_{quarter_end}.csv")
                if not df_quarter.empty:
                    ax2 = ax.twinx()
                    ax2.plot(df_quarter["Quarter"], df_quarter["Signal_Flips_SE"], color="red", linestyle="--", label="Signal Flips SE")
                    ax2.set_ylabel("Signal Flips Standard Error")
                    ax2.legend(loc="upper right")
            except (FileNotFoundError, ValueError):
                messagebox.showerror("Error", f"Invalid quarter or missing stats for {quarter}")
        
        ax.set_title(f"{metric} for {etf} ({start_date} to {end_date})")
        ax.set_xlabel("Date")
        ax.legend(loc="upper left")
        ax.grid(True)
        
        for widget in root.winfo_children():
            if isinstance(widget, FigureCanvasTkAgg):
                widget.get_tk_widget().destroy()
        canvas = FigureCanvasTkAgg(fig, master=root)
        canvas.draw()
        canvas.get_tk_widget().grid(row=8, column=0, columnspan=2, padx=5, pady=5)
    
    tk.Button(root, text="Update Plot", command=update_plot).grid(row=7, column=2, padx=5, pady=5)
    update_plot()
    root.mainloop()

if __name__ == "__main__":
    display_dashboard()