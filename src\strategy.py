import backtrader as bt
import pandas as pd
import numpy as np

class OptionsOverlayStrategy(bt.Strategy):
    params = (
        ("rsi_period", 7), ("rsi_threshold", 40),
        ("macd_fast", 12), ("macd_slow", 26), ("macd_signal", 9),
        ("bb_period", 20), ("bb_std", 2.0),
        ("vix_threshold", 18.0),
        ("ema_short", 8), ("ema_long", 21),
        ("volume_window", 20), ("volume_threshold", 1.5),
        ("wedge_window", 20),
        ("mom_threshold", -0.02), ("rv_threshold", 1.1),
        ("weights", [0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125, 0.125]),
        ("threshold", 0.5), ("ticker", "SPY"), ("capital", 100000),
        ("signal_file", "data/signals.csv"), ("rebalance_type", "last_day"),
        ("consecutive_days", 3), ("hold_to_maturity", True), ("pnl_trigger", 0.05),
    )

    def __init__(self):
        """Initialize the strategy with data and indicators."""
        self.dataclose = self.datas[0].close  # Current closing price
        self.volume = self.datas[0].volume  # Current volume
        self.call_price = self.datas[0].call_price  # Current call option price
        self.put_price = self.datas[0].put_price  # Current put option price
        self.signals = pd.read_csv(self.p.signal_file, index_col="date", parse_dates=True)  # Load signal data
        self.rsi = bt.indicators.RSI(self.datas[0], period=self.p.rsi_period)  # RSI indicator
        self.macd = bt.indicators.MACD(self.datas[0], period_me1=self.p.macd_fast,
                                       period_me2=self.p.macd_slow, period_signal=self.p.macd_signal)  # MACD indicator
        self.bbands = bt.indicators.BollingerBands(self.datas[0], period=self.p.bb_period, devfactor=self.p.bb_std)  # Bollinger Bands
        self.ema_short = bt.indicators.EMA(self.datas[0], period=self.p.ema_short)  # Short EMA
        self.ema_long = bt.indicators.EMA(self.datas[0], period=self.p.ema_long)  # Long EMA
        self.volume_ma = bt.indicators.SMA(self.datas[0].volume, period=self.p.volume_window)  # Volume moving average
        self.commission = 1.00  # Commission per contract
        self.slippage = 0.001  # Slippage percentage
        self.trade_log = []  # List to store trade details
        self.position_active = False  # Track if a position is open
        self.days_to_expiry = 0  # Days remaining until position expiry
        self.current_position = None  # True (covered call), False (ZCC), None (no position)
        self.entry_price = 0  # Entry price of underlying
        self.entry_call_premium = 0  # Entry call premium
        self.entry_put_premium = 0  # Entry put premium
        self.signal_flips_during_hold = 0  # Count of signal flips during hold

    def next(self):
        """Execute trading logic for each time step."""
        date = self.datas[0].datetime.date(0)  # Current date
        price = self.dataclose[0]  # Current price
        call_premium = self.call_price[0]  # Current call premium
        put_premium = self.put_price[0]  # Current put premium
        
        # Update days to expiry and check PnL trigger
        if self.position_active:
            self.days_to_expiry -= 1  # Decrease days to expiry
            current_pnl = self.calculate_pnl(price, call_premium, put_premium)  # Calculate unrealized PnL
            if self.p.hold_to_maturity and (self.days_to_expiry <= 0 or (
                (self.p.pnl_trigger <= 0.15 and abs(current_pnl) >= self.p.pnl_trigger) or
                (self.p.pnl_trigger == 0.25 and abs(current_pnl) >= 0.15 and abs(current_pnl) <= 0.25) or
                (self.p.pnl_trigger == 0.30 and abs(current_pnl) > 0.25)
            )):
                self.close()  # Close position
                self.trade_log.append([date, "close", price, current_pnl, self.commission, self.signal_flips_during_hold])  # Log closure
                self.position_active = False
                self.signal_flips_during_hold = 0
            elif not self.p.hold_to_maturity and (
                (self.p.pnl_trigger <= 0.15 and abs(current_pnl) >= self.p.pnl_trigger) or
                (self.p.pnl_trigger == 0.25 and abs(current_pnl) >= 0.15 and abs(current_pnl) <= 0.25) or
                (self.p.pnl_trigger == 0.30 and abs(current_pnl) > 0.25)
            ):
                self.close()  # Close position
                self.trade_log.append([date, "close", price, current_pnl, self.commission, self.signal_flips_during_hold])  # Log closure
                self.position_active = False
                self.signal_flips_during_hold = 0
        
        # Check intra-period signal flips
        if self.position_active and date in self.signals.index:
            current_trigger = self.signals.loc[date, "trigger"]  # Current signal state
            if current_trigger != self.current_position:
                self.signal_flips_during_hold += 1  # Increment flip count
        
        # Check rebalancing condition
        rebalance = False
        if self.p.rebalance_type == "daily":
            rebalance = True  # Rebalance every day
        elif self.p.rebalance_type == "last_day":
            rebalance = date.is_month_end()  # Rebalance on last day of month
        elif self.p.rebalance_type in ["3_consecutive", "5_consecutive"]:
            consecutive_triggers = self.signals.loc[date, f"consecutive_triggers_{self.p.consecutive_days}"] if date in self.signals.index else 0
            rebalance = consecutive_triggers >= self.p.consecutive_days  # Rebalance if consecutive triggers met
        
        if not rebalance and not (self.position_active and not self.p.hold_to_maturity):
            return
        
        # Technical signals
        rsi_signal = -1 if self.rsi[0] < self.p.rsi_threshold else (1 if self.rsi[0] > 100 - self.p.rsi_threshold else 0)  # RSI signal
        macd_signal = 1 if self.macd.macd[0] > self.macd.signal[0] else -1  # MACD signal
        bb_signal = 1 if self.dataclose[0] > self.bbands.top[0] else (-1 if self.dataclose[0] < self.bbands.bot[0] else 0)  # Bollinger Bands signal
        vix_signal = self.signals.loc[date, "vix_signal"] if (date in self.signals.index and "vix_signal" in self.signals.columns) else 0  # VIX signal
        ema_signal = 1 if self.ema_short[0] > self.ema_long[0] else -1  # EMA signal
        volume_ratio = self.volume[0] / self.volume_ma[0]  # Volume ratio
        volume_signal = 1 if volume_ratio > self.p.volume_threshold else (-1 if volume_ratio < 1/self.p.volume_threshold else 0)  # Volume signal
        wedge_signal = self.signals.loc[date, "wedge_signal"] if (date in self.signals.index and "wedge_signal" in self.signals.columns) else 0  # Wedge signal
        voting_signal = self.signals.loc[date, "voting_signal"] if (date in self.signals.index and "voting_signal" in self.signals.columns) else 0  # Voting signal
        
        # Composite score
        tech_score = sum(w * s for w, s in zip(self.p.weights, [rsi_signal, macd_signal, bb_signal, vix_signal, 
                                                               ema_signal, volume_signal, wedge_signal, voting_signal]))  # Weighted sum of signals
        
        # Combine with quant signal
        quant_signal = self.signals.loc[date, "signal"] if date in self.signals.index else 0  # Quant signal from CSV
        final_score = (tech_score + quant_signal) / 2 if quant_signal != 0 else tech_score  # Final decision score
        
        # Trading logic
        if final_score > self.p.threshold:
            if self.position_active:
                self.close()  # Close existing position
                self.trade_log.append([date, "close", price, self.calculate_pnl(price, call_premium, put_premium), self.commission, self.signal_flips_during_hold])
                self.signal_flips_during_hold = 0
            self.sell(data=self.datas[0], size=100, price=price * 1.05,
                      exectype=bt.Order.Limit, commission=self.commission, slippage=self.slippage)  # Sell OTM call
            self.trade_log.append([date, "sell_call", price * 1.05, call_premium, self.commission, final_score])
            self.position_active = True
            self.current_position = True
            self.days_to_expiry = 30
            self.entry_price = price
            self.entry_call_premium = call_premium
            self.entry_put_premium = 0
        else:
            if self.position_active:
                self.close()  # Close existing position
                self.trade_log.append([date, "close", price, self.calculate_pnl(price, call_premium, put_premium), self.commission, self.signal_flips_during_hold])
                self.signal_flips_during_hold = 0
            self.buy(data=self.datas[0], size=100, price=price * 0.95,
                     exectype=bt.Order.Limit, commission=self.commission, slippage=self.slippage)  # Buy OTM put
            self.sell(data=self.datas[0], size=100, price=price * 1.05,
                      exectype=bt.Order.Limit, commission=self.commission, slippage=self.slippage)  # Sell OTM call
            self.trade_log.append([date, "buy_put", price * 0.95, put_premium, self.commission, final_score])
            self.trade_log.append([date, "sell_call", price * 1.05, call_premium, self.commission, final_score])
            self.position_active = True
            self.current_position = False
            self.days_to_expiry = 30
            self.entry_price = price
            self.entry_call_premium = call_premium
            self.entry_put_premium = put_premium

    def calculate_pnl(self, current_price: float, current_call: float, current_put: float) -> float:
        """Calculate unrealized PnL for current position.

        Args:
            current_price (float): Current underlying price.
            current_call (float): Current call option price.
            current_put (float): Current put option price.

        Returns:
            float: Unrealized PnL as a percentage of entry price.
        """
        if not self.position_active:
            return 0.0
        if self.current_position:  # Covered call
            call_pnl = self.entry_call_premium - current_call  # Change in call premium
            return call_pnl * 100 / self.entry_price  # Normalized per contract
        else:  # ZCC
            call_pnl = self.entry_call_premium - current_call  # Change in call premium
            put_pnl = current_put - self.entry_put_premium  # Change in put premium
            return (put_pnl - call_pnl) * 100 / self.entry_price  # Net PnL normalized

    def stop(self):
        """Save trade log to CSV at the end of the backtest."""
        pd.DataFrame(self.trade_log, columns=["date", "trade_type", "strike", "pnl", "commission", "signal_flips"]).to_csv(
            f"data/output/trade_log_{self.p.ticker}_{self.p.rebalance_type}_pnl{int(self.p.pnl_trigger*100) if self.p.pnl_trigger <= 0.15 else int(self.p.pnl_trigger*100)}.csv", index=False)