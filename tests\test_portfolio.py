import pytest
import pandas as pd
from src.portfolio import optimize_portfolio

def test_optimize_portfolio():
    """Test portfolio optimization with sample data."""
    data = pd.DataFrame({
        "SPY_Price": [100.0] * 100,
        "SCHD_Price": [70.0] * 100,
        "QQQ_Price": [300.0] * 100,
        "IWM_Price": [200.0] * 100,
        "QDIV_Price": [90.0] * 100,
        "QDF_Price": [110.0] * 100
    }, index=pd.date_range("2020-01-01", periods=100))
    weights = optimize_portfolio(data)
    assert not weights.empty
    assert sum(weights.values()) == pytest.approx(1.0, rel=1e-5)
    assert all(ticker in weights for ticker in ["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF"])