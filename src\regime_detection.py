import pandas as pd
import numpy as np
from hmmlearn.hmm import GaussianHMM

def detect_regimes(data: pd.DataFrame, ticker: str = "SPY", n_components: int = 3) -> pd.Series:
    """Detect market regimes using Hidden Markov Model.

    Args:
        data (pd.DataFrame): DataFrame with return data.
        ticker (str): ETF ticker symbol.
        n_components (int): Number of HMM states (e.g., bull, bear, neutral).

    Returns:
        pd.Series: Regime labels for each date.
    """
    returns = data[f"{ticker}_Return"].dropna().values.reshape(-1, 1)
    model = GaussianHMM(n_components=n_components, covariance_type="full", n_iter=100)
    model.fit(returns)
    regimes = model.predict(returns)
    return pd.Series(regimes, index=data.index)