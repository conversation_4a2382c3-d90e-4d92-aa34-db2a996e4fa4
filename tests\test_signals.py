import pytest
import pandas as pd
from src.signals import generate_technical_signals

def test_generate_technical_signals():
    """Test generation of technical signals with sample data."""
    data = pd.DataFrame({
        "SPY_Price": [100.0, 101.0, 102.0, 103.0, 104.0],
        "SPY_Volume": [1000, 1100, 1200, 1300, 1400]
    }, index=pd.date_range("2020-01-01", periods=5))
    vix_data = pd.DataFrame({"VIX": [20.0] * 5}, index=data.index)
    signals = generate_technical_signals(data, "SPY", vix_data)
    assert not signals.empty
    assert all(col in signals.columns for col in ["rsi_signal", "macd_signal", "bb_signal", "vix_signal", "ema_signal", "volume_signal", "wedge_signal", "voting_signal", "trigger"])