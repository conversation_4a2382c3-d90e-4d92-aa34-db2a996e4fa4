import yfinance as yf
import pandas as pd
import os
from datetime import datetime

def fetch_data(tickers: list = None, start_date: str = "1990-01-01", end_date: str = "2025-08-01") -> pd.DataFrame:
    """Fetch daily closing data for tickers using yfinance, with proxies for missing ETFs.

    Args:
        tickers (list): List of tickers (default: SPY, SCHD, QQQ, IWM, QDIV, QDF, JEPI, ^VIX, ^RUT, ^NDX).
        start_date (str): Start date for data (YYYY-MM-DD, default: 1990-01-01).
        end_date (str): End date for data (YYYY-MM-DD, default: 2025-08-01).

    Returns:
        pd.DataFrame: DataFrame with price, return, and volume data.

    Raises:
        ValueError: If data fetch fails or dates are invalid.
    """
    if tickers is None:
        tickers = ["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF", "JEPI", "^VIX", "^RUT", "^NDX"]  # Default tickers
    
    # Validate dates
    try:
        pd.to_datetime(start_date)
        pd.to_datetime(end_date)
    except ValueError as e:
        raise ValueError(f"Invalid date format: {e}")
    
    try:
        # Fetch data from Yahoo Finance
        data = yf.download(tickers, start=start_date, end=end_date, auto_adjust=True, progress=False)
        market_data = pd.DataFrame()
        for ticker in tickers:
            if ticker.startswith("^"):
                market_data[ticker[1:]] = data["Close"][ticker]  # Index data (e.g., VIX)
            else:
                market_data[f"{ticker}_Price"] = data["Close"][ticker]  # ETF price
                market_data[f"{ticker}_Return"] = data["Close"][ticker].pct_change()  # Daily returns
                market_data[f"{ticker}_Volume"] = data["Volume"][ticker]  # Volume
        
        # Proxy missing ETFs
        if "JEPI_Price" in market_data.columns:
            market_data["JEPI_Return"] = market_data["SPY_Return"] + 0.0839 / 252 if start_date < "2020-05-20" else market_data["JEPI_Return"]  # JEPI proxy pre-2020
            market_data["JEPI_Price"] = market_data["SPY_Price"] * (1 + 0.0839 / 252).cumprod() if start_date < "2020-05-20" else market_data["JEPI_Price"]
        if "QDIV_Price" in market_data.columns:
            market_data["QDIV_Return"] = market_data["SCHD_Return"] + 0.005 / 252 if start_date < "2018-09-11" else market_data["QDIV_Return"]  # QDIV proxy pre-2018
            market_data["QDIV_Price"] = market_data["SCHD_Price"] * (1 + 0.005 / 252).cumprod() if start_date < "2018-09-11" else market_data["QDIV_Price"]
        if "QDF_Price" in market_data.columns:
            market_data["QDF_Return"] = market_data["SPY_Return"] + 0.005 / 252 if start_date < "2012-12-12" else market_data["QDF_Return"]  # QDF proxy pre-2012
            market_data["QDF_Price"] = market_data["SPY_Price"] * (1 + 0.005 / 252).cumprod() if start_date < "2012-12-12" else market_data["QDF_Price"]
        
        market_data["VIX_20d_MA"] = market_data["VIX"].rolling(window=20).mean()  # VIX 20-day MA
        market_data = market_data.dropna()  # Remove missing data
        
        # Archive raw data
        os.makedirs("data/archive", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        data.to_csv(f"data/archive/raw_data_{timestamp}.csv")
        with open("data/archive/data_archive_log.txt", "a") as log:
            log.write(f"Archived raw data on {timestamp} for {start_date} to {end_date}\n")
        
        market_data.to_csv("data/market_data.csv")  # Save processed data
        return market_data
    except Exception as e:
        raise ValueError(f"Error fetching data: {e}")