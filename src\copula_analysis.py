import pandas as pd
import numpy as np
from copulas.multivariate import GaussianMultivariate

def copula_tail_risk(data: pd.DataFrame, tickers: list = None) -> dict:
    """Estimate tail risk using Gaussian copula.

    Args:
        data (pd.DataFrame): DataFrame with return data.
        tickers (list): List of ETF tickers.

    Returns:
        dict: VaR and CVaR at 95% and 99% confidence levels.
    """
    if tickers is None:
        tickers = ["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF"]
    returns = data[[f"{ticker}_Return" for ticker in tickers]].dropna()
    copula = GaussianMultivariate()
    copula.fit(returns)
    samples = copula.sample(len(returns))
    portfolio_returns = samples.mean(axis=1)
    var_95 = np.percentile(portfolio_returns, 5)
    var_99 = np.percentile(portfolio_returns, 1)
    cvar_95 = portfolio_returns[portfolio_returns <= var_95].mean()
    cvar_99 = portfolio_returns[portfolio_returns <= var_99].mean()
    return {"VaR_95": var_95, "VaR_99": var_99, "CVaR_95": cvar_95, "CVaR_99": cvar_99}