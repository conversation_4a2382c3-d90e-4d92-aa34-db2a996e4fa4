import pandas as pd
import numpy as np
from QuantLib import *

def estimate_option_prices(data: pd.DataFrame, ticker: str = "SPY") -> pd.DataFrame:
    """Estimate call and put option prices using Black-Scholes model.

    Args:
        data (pd.DataFrame): DataFrame with price and VIX data.
        ticker (str): ETF ticker symbol.

    Returns:
        pd.DataFrame: DataFrame with added call_price and put_price columns.

    Raises:
        ValueError: If required data columns are missing.
    """
    if f"{ticker}_Price" not in data.columns or "VIX" not in data.columns:
        raise ValueError("Missing required columns in data")
    
    data = data.copy()
    data["call_price"] = 0.0
    data["put_price"] = 0.0
    
    # Black-Scholes parameters
    risk_free_rate = 0.02  # 2% annual risk-free rate
    dividend_yield = 0.02  # 2% dividend yield
    days_to_expiry = 30
    
    for date in data.index:
        spot_price = data.loc[date, f"{ticker}_Price"]
        volatility = data.loc[date, "VIX"] / 100  # VIX as implied volatility
        call_strike = spot_price * 1.05  # 5% OTM call
        put_strike = spot_price * 0.95  # 5% OTM put
        
        # Set up QuantLib
        calendar = UnitedStates()
        day_count = Actual365Fixed()
        calculation_date = Date(date.day, date.month, date.year)
        Settings.instance().evaluationDate = calculation_date
        
        # Option parameters
        option_type_call = Option.Call
        option_type_put = Option.Put
        payoff_call = PlainVanillaPayoff(option_type_call, call_strike)
        payoff_put = PlainVanillaPayoff(option_type_put, put_strike)
        exercise = EuropeanExercise(calculation_date + days_to_expiry)
        
        # Market data
        spot_handle = QuoteHandle(SimpleQuote(spot_price))
        flat_ts = YieldTermStructureHandle(FlatForward(calculation_date, risk_free_rate, day_count))
        dividend_ts = YieldTermStructureHandle(FlatForward(calculation_date, dividend_yield, day_count))
        flat_vol_ts = BlackVolTermStructureHandle(BlackConstantVol(calculation_date, calendar, volatility, day_count))
        
        # Black-Scholes process
        bsm_process = BlackScholesMertonProcess(spot_handle, dividend_ts, flat_ts, flat_vol_ts)
        
        # Price options
        european_option_call = VanillaOption(payoff_call, exercise)
        european_option_put = VanillaOption(payoff_put, exercise)
        european_option_call.setPricingEngine(AnalyticEuropeanEngine(bsm_process))
        european_option_put.setPricingEngine(AnalyticEuropeanEngine(bsm_process))
        
        data.loc[date, "call_price"] = european_option_call.NPV()
        data.loc[date, "put_price"] = european_option_put.NPV()
    
    return data