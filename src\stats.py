import pandas as pd
import numpy as np
import empyrical as ep

def calculate_advanced_risk_stats(data: pd.DataFrame, backtest_results: dict, ticker: str = "SPY") -> pd.DataFrame:
    """Calculate advanced risk metrics for backtest results.

    Args:
        data (pd.DataFrame): DataFrame with return data.
        backtest_results (dict): Backtest results dictionary.
        ticker (str): ETF ticker symbol.

    Returns:
        pd.DataFrame: DataFrame with risk metrics.
    """
    monthly_returns = data[f"{ticker}_Return"].resample("ME").sum()  # Aggregate returns monthly
    jepi_returns = data["JEPI_Return"].resample("ME").sum() if "JEPI_Return" in data.columns else data["SPY_Return"] + 0.0839 / 12  # JEPI benchmark
    stats = []
    
    for trigger_type in ["daily", "last_day", "3_consecutive", "5_consecutive"]:
        for pnl_trigger in [0.05, 0.10, 0.25, 0.30]:
            strategy = f"{ticker.lower()}_timing_vix18_{trigger_type}_pnl{int(pnl_trigger*100) if pnl_trigger <= 0.15 else int(pnl_trigger*100)}"
            if strategy in backtest_results:
                df = backtest_results[strategy]
                returns = df["Total_Return"].values
                benchmark_returns = jepi_returns[df["Date"]].values
                sharpe = ep.sharpe_ratio(returns, risk_free=0.02, annualization=12)  # Sharpe ratio
                sortino = ep.sortino_ratio(returns, required_return=0.02, annualization=12)  # Sortino ratio
                batting_avg = np.mean(returns > benchmark_returns) * 100  # Batting average
                upside_capture = ep.upside_capture(returns, benchmark_returns) * 100  # Upside capture
                downside_capture = ep.downside_capture(returns, benchmark_returns) * 100  # Downside capture
                calmar = ep.calmar_ratio(returns, annualization=12)  # Calmar ratio
                max_drawdown = ep.max_drawdown(returns) * 100  # Maximum drawdown
                omega = ep.omega_ratio(returns, risk_free=0.02, annualization=12)  # Omega ratio
                drawdown_duration = len(df[df["Fund_Cumulative"] < df["Fund_Cumulative"].cummax()]) / 12 if max_drawdown > 0 else 0  # Drawdown duration
                information_ratio = ep.excess_sharpe(returns, benchmark_returns, risk_free=0.02, annualization=12)  # Information ratio
                beta = np.cov(returns, benchmark_returns)[0,1] / np.var(benchmark_returns)  # Beta
                jensen_alpha = (np.mean(returns) * 12 - 0.02) - beta * (np.mean(data["SPY_Return"]) * 12 - 0.02)  # Jensen's alpha
                
                stats.append({
                    "Ticker": ticker,
                    "Trigger": trigger_type,
                    "PnL Trigger": pnl_trigger,
                    "Sharpe Ratio": sharpe,
                    "Sortino Ratio": sortino,
                    "Batting Average (%)": batting_avg,
                    "Upside Capture (%)": upside_capture,
                    "Downside Capture (%)": downside_capture,
                    "Calmar Ratio": calmar,
                    "Max Drawdown (%)": max_drawdown,
                    "Omega Ratio": omega,
                    "Drawdown Duration (years)": drawdown_duration,
                    "Alpha vs JEPI (%)": (np.mean(returns) * 12 - 0.107) * 100,
                    "Information Ratio": information_ratio,
                    "Jensen Alpha (%)": jensen_alpha * 100
                })
    
    results_df = pd.DataFrame(stats)
    results_df.to_csv(f"data/output/risk_stats_{ticker}.csv", index=False)
    results_df.to_excel(f"data/output/risk_stats_{ticker}.xlsx", index=False)
    return results_df

def calculate_signal_stats(data: pd.DataFrame, signals: pd.DataFrame, ticker: str = "SPY") -> pd.DataFrame:
    """Calculate signal accuracy and stability for predicting drops.

    Args:
        data (pd.DataFrame): DataFrame with return data.
        signals (pd.DataFrame): DataFrame with signal data.
        ticker (str): ETF ticker symbol.

    Returns:
        pd.DataFrame: DataFrame with signal statistics.
    """
    monthly_returns = data[f"{ticker}_Return"].resample("ME").sum()
    stats = []
    
    for drop_threshold in [-0.05, -0.03]:
        for trigger_type in ["daily", "last_day", "3_consecutive", "5_consecutive"]:
            for pnl_trigger in [0.05, 0.10, 0.25, 0.30]:
                if trigger_type == "daily":
                    signal_trigger = signals["voting_signal"]
                elif trigger_type == "last_day":
                    signal_trigger = signals["voting_signal"].resample("ME").last()
                elif trigger_type in ["3_consecutive", "5_consecutive"]:
                    signal_trigger = signals[f"consecutive_triggers_{trigger_type.split('_')[0]}"] >= int(trigger_type.split("_")[0])
                    signal_trigger = signal_trigger.resample("ME").last()
                drops = monthly_returns.shift(-1) < drop_threshold
                true_positives = sum(signal_trigger[:-1] & drops[:-1])
                false_positives = sum(signal_trigger[:-1] & ~drops[:-1])
                accuracy = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
                signal_flips_mean = signals["signal_flips"].mean()
                signal_flips_se = signals["signal_flips"].std() / np.sqrt(len(signals)) if len(signals) > 0 else 0
                ic = signals["voting_signal"].corr(monthly_returns.shift(-1))  # Information coefficient
                
                stats.append({
                    "Ticker": ticker,
                    "Trigger": trigger_type,
                    "PnL Trigger": pnl_trigger,
                    "Drop Threshold": drop_threshold,
                    "True Positives": true_positives,
                    "False Positives": false_positives,
                    "Accuracy": accuracy,
                    "Signal Flips Mean": signal_flips_mean,
                    "Signal Flips SE": signal_flips_se,
                    "Information Coefficient": ic
                })
    
    results_df = pd.DataFrame(stats)
    results_df.to_csv(f"data/output/signal_stats_{ticker}.csv", index=False)
    results_df.to_excel(f"data/output/signal_stats_{ticker}.xlsx", index=False)
    return results_df