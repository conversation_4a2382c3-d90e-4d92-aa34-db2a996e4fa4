import pandas as pd
import numpy as np
from pypfopt.efficient_frontier import EfficientFrontier
from pypfopt import risk_models, expected_returns

def optimize_portfolio(data: pd.DataFrame) -> dict:
    """Optimize portfolio weights to maximize Sharpe ratio.

    Args:
        data (pd.DataFrame): DataFrame with price data for multiple ETFs.

    Returns:
        dict: Optimized weights for each ETF.

    Raises:
        ValueError: If insufficient data or missing price columns.
    """
    # Validate inputs
    required_tickers = ["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF"]
    price_cols = [f"{ticker}_Price" for ticker in required_tickers]
    if not all(col in data.columns for col in price_cols):
        raise ValueError(f"Missing price columns for {required_tickers}")
    
    # Extract price data
    prices = data[price_cols]
    
    # Calculate expected returns and sample covariance
    mu = expected_returns.mean_historical_return(prices)
    S = risk_models.sample_cov(prices)
    
    # Optimize portfolio for maximum Sharpe ratio
    ef = EfficientFrontier(mu, S)
    weights = ef.max_sharpe(risk_free_rate=0.02)
    cleaned_weights = ef.clean_weights()
    
    return cleaned_weights