import pandas as pd
import talib
import numpy as np
from sklearn.linear_model import LogisticRegression

def generate_technical_signals(data: pd.DataFrame, ticker: str = "SPY", vix_data: pd.DataFrame = None,
                              rsi_period: int = 7, rsi_threshold: float = 40,
                              macd_fast: int = 12, macd_slow: int = 26, macd_signal: int = 9,
                              bb_period: int = 20, bb_std: float = 2.0,
                              vix_threshold: float = 18.0,
                              ema_short: int = 8, ema_long: int = 21,
                              volume_window: int = 20, volume_threshold: float = 1.5,
                              wedge_window: int = 20,
                              mom_threshold: float = -0.02, rv_threshold: float = 1.1,
                              consecutive_days: int = 3) -> pd.DataFrame:
    """Generate technical signals and track consecutive trigger days and flips.

    Args:
        data (pd.DataFrame): DataFrame with price and volume data.
        ticker (str): ETF ticker symbol.
        vix_data (pd.DataFrame): DataFrame with VIX data.
        rsi_period (int): Period for RSI calculation.
        rsi_threshold (float): RSI threshold for overbought/oversold signals.
        macd_fast (int): Fast period for MACD.
        macd_slow (int): Slow period for MACD.
        macd_signal (int): Signal period for MACD.
        bb_period (int): Period for Bollinger Bands.
        bb_std (float): Standard deviation for Bollinger Bands.
        vix_threshold (float): VIX threshold for signals.
        ema_short (int): Short period for EMA crossover.
        ema_long (int): Long period for EMA crossover.
        volume_window (int): Window for volume moving average.
        volume_threshold (float): Volume threshold for spike signals.
        wedge_window (int): Window for wedge pattern detection.
        mom_threshold (float): Momentum threshold for signals.
        rv_threshold (float): Relative volatility threshold.
        consecutive_days (int): Number of consecutive days for trigger.

    Returns:
        pd.DataFrame: Signals with columns for each signal and flip counts.

    Raises:
        ValueError: If required data columns are missing or invalid parameters.
    """
    # Validate inputs
    price_col = f"{ticker}_Price"
    volume_col = f"{ticker}_Volume"
    if price_col not in data.columns or volume_col not in data.columns:
        raise ValueError(f"Missing {price_col} or {volume_col} in data")
    if vix_data is not None and "VIX" not in vix_data.columns:
        raise ValueError("Missing VIX column in vix_data")
    
    signals = pd.DataFrame(index=data.index)
    
    # RSI_7: Calculate Relative Strength Index
    signals["rsi"] = talib.RSI(data[price_col], timeperiod=rsi_period)
    signals["rsi_signal"] = 0
    signals.loc[signals["rsi"] < rsi_threshold, "rsi_signal"] = -1  # Oversold (bearish)
    signals.loc[signals["rsi"] > 100 - rsi_threshold, "rsi_signal"] = 1  # Overbought (bullish)
    
    # MACD: Calculate Moving Average Convergence Divergence
    signals["macd"], signals["macd_signal"], _ = talib.MACD(data[price_col], fastperiod=macd_fast,
                                                            slowperiod=macd_slow, signalperiod=macd_signal)
    signals["macd_signal"] = 0
    signals.loc[signals["macd"] > signals["macd_signal"], "macd_signal"] = 1  # Bullish crossover
    signals.loc[signals["macd"] < signals["macd_signal"], "macd_signal"] = -1  # Bearish crossover
    
    # Bollinger Bands: Calculate price bands for volatility signals
    signals["bb_upper"], signals["bb_middle"], signals["bb_lower"] = talib.BBANDS(data[price_col],
                                                                                 timeperiod=bb_period, nbdevup=bb_std, nbdevdn=bb_std)
    signals["bb_signal"] = 0
    signals.loc[data[price_col] > signals["bb_upper"], "bb_signal"] = 1  # Above upper band (bullish)
    signals.loc[data[price_col] < signals["bb_lower"], "bb_signal"] = -1  # Below lower band (bearish)
    
    # VIX: Volatility index signals
    if vix_data is not None and not vix_data.empty:
        signals["vix"] = vix_data["VIX"].reindex(signals.index, method="ffill")
        signals["vix_20d_ma"] = signals["vix"].rolling(window=20).mean()
        signals["vix_signal"] = 0
        signals.loc[(signals["vix"] > vix_threshold) | (signals["vix"] > rv_threshold * signals["vix_20d_ma"]), "vix_signal"] = -1  # High volatility (bearish)
        signals.loc[signals["vix"] < vix_threshold, "vix_signal"] = 1  # Low volatility (bullish)
    
    # EMA Crossover: Exponential Moving Average signals
    signals["ema_short"] = talib.EMA(data[price_col], timeperiod=ema_short)
    signals["ema_long"] = talib.EMA(data[price_col], timeperiod=ema_long)
    signals["ema_signal"] = 0
    signals.loc[signals["ema_short"] > signals["ema_long"], "ema_signal"] = 1  # Bullish crossover
    signals.loc[signals["ema_short"] < signals["ema_long"], "ema_signal"] = -1  # Bearish crossover
    
    # Volume Spike: Detect abnormal volume changes
    signals["volume_ma"] = data[volume_col].rolling(window=volume_window).mean()
    signals["volume_ratio"] = data[volume_col] / signals["volume_ma"]
    signals["volume_signal"] = 0
    signals.loc[signals["volume_ratio"] > volume_threshold, "volume_signal"] = 1  # High volume (bullish)
    signals.loc[signals["volume_ratio"] < 1/volume_threshold, "volume_signal"] = -1  # Low volume (bearish)
    
    # Wedge Pattern: Detect converging price patterns
    signals["highs"] = data[price_col].rolling(window=wedge_window).max()
    signals["lows"] = data[price_col].rolling(window=wedge_window).min()
    signals["wedge_signal"] = 0
    signals["highs_diff"] = signals["highs"].diff()
    signals["lows_diff"] = signals["lows"].diff()
    signals.loc[(signals["highs_diff"] < 0) & (signals["lows_diff"] > 0), "wedge_signal"] = 1  # Bullish wedge
    signals.loc[(signals["highs_diff"] > 0) & (signals["lows_diff"] < 0), "wedge_signal"] = -1  # Bearish wedge
    
    # Momentum: 20-day price change signals
    signals["mom"] = data[price_col].pct_change(periods=20)
    signals["mom_signal"] = 0
    signals.loc[signals["mom"] < mom_threshold, "mom_signal"] = -1  # Negative momentum (bearish)
    signals.loc[signals["mom"] > -mom_threshold, "mom_signal"] = 1  # Positive momentum (bullish)
    
    # Relative Volatility (RV): VIX relative to its moving average
    signals["rv"] = signals["vix"] / signals["vix_20d_ma"]
    signals["rv_signal"] = 0
    signals.loc[signals["rv"] > rv_threshold, "rv_signal"] = -1  # High RV (bearish)
    signals.loc[signals["rv"] < rv_threshold, "rv_signal"] = 1  # Low RV (bullish)
    
    # 2/3 Voting: Majority voting for VIX, EMA, MOM
    signals["voting_signal"] = 0
    signals["vote_sum"] = signals["vix_signal"] + signals["ema_signal"] + signals["mom_signal"]
    signals.loc[signals["vote_sum"] >= 2, "voting_signal"] = 1  # At least 2 bullish
    signals.loc[signals["vote_sum"] <= -2, "voting_signal"] = -1  # At least 2 bearish
    
    # Composite score: Average of all signals
    signals["composite_score"] = signals[["rsi_signal", "macd_signal", "bb_signal", "vix_signal", 
                                         "ema_signal", "volume_signal", "wedge_signal", "voting_signal"]].mean(axis=1)
    signals["trigger"] = signals["composite_score"] <= signals["composite_score"].mean()  # Bearish if score <= mean
    
    # Consecutive trigger days
    signals["consecutive_triggers_3"] = signals["trigger"].rolling(window=3).sum()
    signals["consecutive_triggers_5"] = signals["trigger"].rolling(window=5).sum()
    
    # Signal flips: Count changes in trigger within 30-day windows
    signals["signal_flips"] = signals["trigger"].diff().abs().rolling(window=30).sum()
    
    return signals

def calculate_signal_weights(data: pd.DataFrame, signals: pd.DataFrame, ticker: str = "SPY", drop_threshold: float = -0.05) -> dict:
    """Calculate signal weights using Logistic Regression.

    Args:
        data (pd.DataFrame): DataFrame with return data.
        signals (pd.DataFrame): DataFrame with signal data.
        ticker (str): ETF ticker symbol.
        drop_threshold (float): Threshold for significant price drops.

    Returns:
        dict: Weights for VIX, EMA, MOM signals and score threshold.

    Raises:
        ValueError: If insufficient data for fitting model.
    """
    # Validate inputs
    if f"{ticker}_Return" not in data.columns:
        raise ValueError(f"Missing {ticker}_Return in data")
    if not all(col in signals.columns for col in ["vix_signal", "ema_signal", "mom_signal"]):
        raise ValueError("Missing required signal columns")
    
    # Prepare data for logistic regression
    X = signals[["vix_signal", "ema_signal", "mom_signal"]].resample("ME").last()
    y = data[f"{ticker}_Return"].resample("ME").sum().shift(-1) < drop_threshold
    if len(X) < 2 or len(y) < 2:
        return {"weights": {"vix_signal": 0.4, "ema_signal": 0.35, "mom_signal": 0.25}, "threshold": 0.5}
    
    # Fit logistic regression model
    model = LogisticRegression().fit(X[:-1], y[:-1])
    weights = dict(zip(["vix_signal", "ema_signal", "mom_signal"], model.coef_[0]))
    score = sum(model.coef_[0][i] * X.iloc[:, i] for i in range(3))
    return {"weights": weights, "threshold": score.mean()}