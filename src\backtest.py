import backtrader as bt
import pandas as pd
import numpy as np
import itertools
import dask
from dask.distributed import Client
from src.data_fetcher import fetch_data, estimate_option_prices
from src.signals import generate_technical_signals, calculate_signal_weights
from src.strategy import OptionsOverlayStrategy
from src.stats import calculate_advanced_risk_stats, calculate_signal_stats
from src.portfolio import optimize_portfolio
from src.regime_detection import detect_regimes
from src.copula_analysis import copula_tail_risk
from datetime import datetime, timedelta
from rich.console import Console
from rich.table import Table
import argparse

def run_single_backtest(params, ticker="SPY", start_date="1990-01-01", end_date="2025-08-01", reference_start="2015-08-01", reference_end="2025-08-01"):
    """Run a single backtest for given parameters with reference period comparison.

    Args:
        params (tuple): Strategy parameters (rsi_p, rsi_t, ..., pnl_trigger).
        ticker (str): ETF ticker symbol.
        start_date (str): Start date for backtest.
        end_date (str): End date for backtest.
        reference_start (str): Reference period start (2015-08-01).
        reference_end (str): Reference period end (2025-08-01).

    Returns:
        dict: Backtest results including metrics and stability statistics.
    """
    console = Console()
    rsi_p, rsi_t, macd_f, macd_s, macd_sig, bb_p, bb_std, vix_t, ema_s, ema_l, vol_w, vol_t, wedge_w, mom_t, rv_t, weights, thresh, rebalance_type, consecutive_days, hold_to_maturity, pnl_trigger = params
    
    # Fetch and prepare data
    try:
        data = fetch_data([ticker, "^VIX"], start_date, end_date)
        ref_data = fetch_data([ticker, "^VIX"], reference_start, reference_end)
    except ValueError as e:
        console.print(f"[red]Data fetch error: {e}[/red]")
        return None
    vix_data = data[["VIX", "VIX_20d_MA"]]
    data = estimate_option_prices(data, ticker)
    ref_vix_data = ref_data[["VIX", "VIX_20d_MA"]]
    ref_data = estimate_option_prices(ref_data, ticker)
    
    # Generate signals
    signals = generate_technical_signals(data, ticker, vix_data, rsi_period=rsi_p, rsi_threshold=rsi_t,
                                        macd_fast=macd_f, macd_slow=macd_s, macd_signal=macd_sig,
                                        bb_period=bb_p, bb_std=bb_std, vix_threshold=vix_t,
                                        ema_short=ema_s, ema_long=ema_l, volume_window=vol_w, volume_threshold=vol_t,
                                        wedge_window=wedge_w, mom_threshold=mom_t, rv_threshold=rv_t,
                                        consecutive_days=consecutive_days)
    ref_signals = generate_technical_signals(ref_data, ticker, ref_vix_data, rsi_period=rsi_p, rsi_threshold=rsi_t,
                                            macd_fast=macd_f, macd_slow=macd_s, macd_signal=macd_sig,
                                            bb_period=bb_p, bb_std=bb_std, vix_threshold=vix_t,
                                            ema_short=ema_s, ema_long=ema_l, volume_window=vol_w, volume_threshold=vol_t,
                                            wedge_window=wedge_w, mom_threshold=mom_t, rv_threshold=rv_t,
                                            consecutive_days=consecutive_days)
    signals[["rsi_signal", "macd_signal", "bb_signal", "vix_signal", "ema_signal", "volume_signal", "wedge_signal", "voting_signal", "consecutive_triggers_3", "consecutive_triggers_5", "signal_flips"]].to_csv(
        f"data/output/signals_{ticker}_{rebalance_type}_pnl{int(pnl_trigger*100) if pnl_trigger <= 0.15 else int(pnl_trigger*100)}.csv")
    
    # Detect regimes
    regimes = detect_regimes(data, ticker)
    
    # Calculate tail risk
    tail_risk = copula_tail_risk(data, [ticker])
    
    # Prepare data feed for Backtrader
    data_feed = bt.feeds.PandasData(dataname=data, open=f"{ticker}_Price", high=f"{ticker}_Price", low=f"{ticker}_Price", close=f"{ticker}_Price",
                                    volume=f"{ticker}_Volume", call_price="call_price", put_price="put_price")
    ref_data_feed = bt.feeds.PandasData(dataname=ref_data, open=f"{ticker}_Price", high=f"{ticker}_Price", low=f"{ticker}_Price", close=f"{ticker}_Price",
                                        volume=f"{ticker}_Volume", call_price="call_price", put_price="put_price")
    
    # Run backtest
    cerebro = bt.Cerebro()
    cerebro.addstrategy(OptionsOverlayStrategy,
                       rsi_period=rsi_p, rsi_threshold=rsi_t,
                       macd_fast=macd_f, macd_slow=macd_s, macd_signal=macd_sig,
                       bb_period=bb_p, bb_std=bb_std, vix_threshold=vix_t,
                       ema_short=ema_s, ema_long=ema_l, volume_window=vol_w, volume_threshold=vol_t,
                       wedge_window=wedge_w, mom_threshold=mom_t, rv_threshold=rv_t,
                       weights=weights, threshold=thresh, ticker=ticker,
                       rebalance_type=rebalance_type, consecutive_days=consecutive_days,
                       hold_to_maturity=hold_to_maturity, pnl_trigger=pnl_trigger)
    cerebro.adddata(data_feed)
    cerebro.broker.set_cash(100000)
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
    cerebro.addanalyzer(bt.analyzers.Returns, _name="returns")
    run = cerebro.run()
    
    # Run reference period backtest
    ref_cerebro = bt.Cerebro()
    ref_cerebro.addstrategy(OptionsOverlayStrategy,
                           rsi_period=rsi_p, rsi_threshold=rsi_t,
                           macd_fast=macd_f, macd_slow=macd_s, macd_signal=macd_sig,
                           bb_period=bb_p, bb_std=bb_std, vix_threshold=vix_t,
                           ema_short=ema_s, ema_long=ema_l, volume_window=vol_w, volume_threshold=vol_t,
                           wedge_window=wedge_w, mom_threshold=mom_t, rv_threshold=rv_t,
                           weights=weights, threshold=thresh, ticker=ticker,
                           rebalance_type=rebalance_type, consecutive_days=consecutive_days,
                           hold_to_maturity=hold_to_maturity, pnl_trigger=pnl_trigger)
    ref_cerebro.adddata(ref_data_feed)
    ref_cerebro.broker.set_cash(100000)
    ref_cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
    ref_cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
    ref_cerebro.addanalyzer(bt.analyzers.Returns, _name="returns")
    ref_run = ref_cerebro.run()
    
    # Extract results
    returns = run[0].analyzers.returns.get_analysis()
    ref_returns = ref_run[0].analyzers.returns.get_analysis()
    backtest_results = {f"{ticker}_timing_vix{vix_t}_{rebalance_type}_pnl{int(pnl_trigger*100) if pnl_trigger <= 0.15 else int(pnl_trigger*100)}": pd.DataFrame({
        "Date": data.index,
        "Total_Return": data[f"{ticker}_Return"],
        "Fund_Cumulative": (1 + data[f"{ticker}_Return"]).cumprod() * 100000
    })}
    
    # Calculate risk and signal stats
    stats = calculate_advanced_risk_stats(data, backtest_results, ticker)
    signal_stats = calculate_signal_stats(data, signals, ticker)
    ref_stats = calculate_advanced_risk_stats(ref_data, backtest_results, ticker)
    
    # Calculate signal stability
    signal_flips = signals["signal_flips"].mean()
    
    # Display results with rich
    table = Table(title=f"Backtest Results for {ticker} ({start_date} to {end_date})")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green" if run[0].analyzers.sharpe.get_analysis().get("sharperatio", 0) > 1 else "red")
    table.add_column("Reference (2015–2025)", style="yellow")
    table.add_row("Sharpe Ratio", f"{run[0].analyzers.sharpe.get_analysis().get('sharperatio', 0):.2f}", f"{ref_run[0].analyzers.sharpe.get_analysis().get('sharperatio', 0):.2f}")
    table.add_row("Max Drawdown (%)", f"{run[0].analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}", f"{ref_run[0].analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}")
    table.add_row("Return (%)", f"{returns['rtot']*100:.2f}", f"{ref_returns['rtot']*100:.2f}")
    table.add_row("Alpha vs JEPI (%)", f"{(returns['rtot'] - 0.107)*100:.2f}", f"{(ref_returns['rtot'] - 0.107)*100:.2f}")
    table.add_row("Signal Flips (30-day)", f"{signal_flips:.2f}", f"{ref_signals['signal_flips'].mean():.2f}")
    table.add_row("VaR 95% (%)", f"{tail_risk['VaR_95']*100:.2f}", "-")
    table.add_row("CVaR 95% (%)", f"{tail_risk['CVaR_95']*100:.2f}", "-")
    console.print(table)
    
    return {
        "params": params,
        "sharpe": run[0].analyzers.sharpe.get_analysis().get("sharperatio", 0),
        "max_drawdown": run[0].analyzers.drawdown.get_analysis()["max"]["drawdown"],
        "return": returns["rtot"],
        "alpha_vs_jepi": returns["rtot"] - 0.107,
        "stats": stats.to_dict(),
        "signal_stats": signal_stats.to_dict(),
        "signal_flips": signal_flips,
        "ref_sharpe": ref_run[0].analyzers.sharpe.get_analysis().get("sharperatio", 0),
        "ref_max_drawdown": ref_run[0].analyzers.drawdown.get_analysis()["max"]["drawdown"],
        "ref_return": ref_returns["rtot"],
        "ref_alpha_vs_jepi": ref_returns["rtot"] - 0.107,
        "var_95": tail_risk["VaR_95"],
        "cvar_95": tail_risk["CVaR_95"]
    }

def run_backtest(ticker: str = "SPY", start_date: str = "1990-01-01", end_date: str = "2025-08-01", quarterly_review: bool = False):
    """Run MCP backtest with parallel processing and quarterly review.

    Args:
        ticker (str): ETF ticker symbol.
        start_date (str): Start date for backtest.
        end_date (str): End date for backtest.
        quarterly_review (bool): Run quarterly reviews if True.

    Returns:
        None: Outputs results to CSV/Excel.
    """
    console = Console()
    # Define MCP parameter grid
    rsi_periods = [7, 14]
    rsi_thresholds = [30, 40]
    macd_fasts = [9, 12]
    macd_slows = [21, 26]
    macd_signals = [9, 12]
    bb_periods = [20, 50]
    bb_stds = [2.0, 2.5]
    vix_thresholds = [15, 18, 19]
    ema_shorts = [8, 10]
    ema_longs = [21, 34, 50]
    volume_windows = [20, 50]
    volume_thresholds = [1.5, 2.0]
    wedge_windows = [20, 50]
    mom_thresholds = [-0.03, -0.02]
    rv_thresholds = [1.0, 1.1]
    weight_combinations = [list(w) for w in np.random.dirichlet([1]*8, 1000)]
    thresholds = [0.4, 0.5, 0.6]
    rebalance_types = ["daily", "last_day", "3_consecutive", "5_consecutive"]
    consecutive_days = [3, 5]
    hold_to_maturity_options = [True, False]
    pnl_triggers = [0.05, 0.10, 0.25, 0.30]
    
    param_combinations = list(itertools.product(rsi_periods, rsi_thresholds, macd_fasts, macd_slows, macd_signals,
                                               bb_periods, bb_stds, vix_thresholds, ema_shorts, ema_longs,
                                               volume_windows, volume_thresholds, wedge_windows, mom_thresholds,
                                               rv_thresholds, weight_combinations, thresholds, rebalance_types,
                                               consecutive_days, hold_to_maturity_options, pnl_triggers))
    
    param_combinations = [p for p in param_combinations if (p[-4] == "last_day" and p[-3] == 3) or (p[-4] == f"{p[-3]}_consecutive") or (p[-4] == "daily" and p[-3] == 3)]
    
    # Optimize portfolio weights
    try:
        data = fetch_data(["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF"], start_date, end_date)
        portfolio_weights = optimize_portfolio(data)
        console.print("[bold green]Optimized Portfolio Weights:[/bold green]", portfolio_weights)
    except ValueError as e:
        console.print(f"[red]Portfolio optimization error: {e}[/red]")
        portfolio_weights = {ticker: 1/6 for ticker in ["SPY", "SCHD", "QQQ", "IWM", "QDIV", "QDF"]}
    
    # Run backtests with Dask
    client = Client()
    results = []
    if quarterly_review:
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        quarters = pd.date_range(start, end, freq="3ME")
        for quarter_end in quarters:
            quarter_start = quarter_end - pd.Timedelta(days=90)
            quarter_results = dask.compute(*[dask.delayed(run_single_backtest)(params, ticker, quarter_start.strftime("%Y-%m-%d"), quarter_end.strftime("%Y-%m-%d")) for params in param_combinations[:5000]])
            results.extend([r for r in quarter_results if r is not None])
            
            quarter_stats = pd.DataFrame([{
                "Quarter": quarter_end.strftime("%Y-%m-%d"),
                "Sharpe_Mean": np.mean([r["sharpe"] for r in quarter_results if r is not None]),
                "Sharpe_SE": np.std([r["sharpe"] for r in quarter_results if r is not None]) / np.sqrt(len([r for r in quarter_results if r is not None])),
                "Return_Mean": np.mean([r["return"] for r in quarter_results if r is not None]),
                "Return_SE": np.std([r["return"] for r in quarter_results if r is not None]) / np.sqrt(len([r for r in quarter_results if r is not None])),
                "Drawdown_Mean": np.mean([r["max_drawdown"] for r in quarter_results if r is not None]),
                "Drawdown_SE": np.std([r["max_drawdown"] for r in quarter_results if r is not None]) / np.sqrt(len([r for r in quarter_results if r is not None])),
                "Signal_Flips_Mean": np.mean([r["signal_flips"] for r in quarter_results if r is not None]),
                "Signal_Flips_SE": np.std([r["signal_flips"] for r in quarter_results if r is not None]) / np.sqrt(len([r for r in quarter_results if r is not None])),
                "VaR_95": np.mean([r["var_95"] for r in quarter_results if r is not None]),
                "CVaR_95": np.mean([r["cvar_95"] for r in quarter_results if r is not None])
            } for r in quarter_results if r is not None])
            quarter_stats.to_csv(f"data/output/quarterly_stats_{ticker}_{quarter_end.strftime('%Y%m%d')}.csv", index=False)
            quarter_stats.to_excel(f"data/output/quarterly_stats_{ticker}_{quarter_end.strftime('%Y%m%d')}.xlsx", index=False)
    else:
        results = dask.compute(*[dask.delayed(run_single_backtest)(params, ticker, start_date, end_date) for params in param_combinations[:5000]])
        results = [r for r in results if r is not None]
    
    client.close()
    
    # Compile results
    results_df = pd.DataFrame([{
        "ticker": ticker,
        "params": r["params"],
        "sharpe": r["sharpe"],
        "max_drawdown": r["max_drawdown"],
        "return": r["return"],
        "alpha_vs_jepi": r["alpha_vs_jepi"],
        "stats": r["stats"],
        "signal_stats": r["signal_stats"],
        "signal_flips": r["signal_flips"],
        "ref_sharpe": r["ref_sharpe"],
        "ref_max_drawdown": r["ref_max_drawdown"],
        "ref_return": r["ref_return"],
        "ref_alpha_vs_jepi": r["ref_alpha_vs_jepi"],
        "var_95": r["var_95"],
        "cvar_95": r["cvar_95"]
    } for r in results])
    results_df.to_csv(f"data/output/mcp_results_{ticker}_{start_date}_{end_date}.csv", index=False)
    results_df.to_excel(f"data/output/mcp_results_{ticker}_{start_date}_{end_date}.xlsx", index=False)
    
    # Display best results with rich
    if not results_df.empty:
        best_params = results_df.loc[results_df["sharpe"].idxmax()]
        table = Table(title=f"Best Parameters for {ticker} ({start_date} to {end_date})")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green" if best_params["sharpe"] > 1 else "red")
        table.add_column("Reference (2015–2025)", style="yellow")
        table.add_row("Sharpe Ratio", f"{best_params['sharpe']:.2f}", f"{best_params['ref_sharpe']:.2f}")
        table.add_row("Max Drawdown (%)", f"{best_params['max_drawdown']:.2f}", f"{best_params['ref_max_drawdown']:.2f}")
        table.add_row("Return (%)", f"{best_params['return']*100:.2f}", f"{best_params['ref_return']*100:.2f}")
        table.add_row("Alpha vs JEPI (%)", f"{best_params['alpha_vs_jepi']*100:.2f}", f"{best_params['ref_alpha_vs_jepi']*100:.2f}")
        table.add_row("Signal Flips (30-day)", f"{best_params['signal_flips']:.2f}", f"{results_df['signal_flips'].mean():.2f}")
        table.add_row("VaR 95% (%)", f"{best_params['var_95']*100:.2f}", "-")
        table.add_row("CVaR 95% (%)", f"{best_params['cvar_95']*100:.2f}", "-")
        console.print(table)
        console.print(f"[bold cyan]Parameters:[/bold cyan] {best_params['params']}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run AdaptiveCapture Fund backtest")
    parser.add_argument("--ticker", default="SPY", help="ETF ticker symbol")
    parser.add_argument("--start_date", default="1990-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end_date", default="2025-08-01", help="End date (YYYY-MM-DD)")
    parser.add_argument("--quarterly_review", action="store_true", help="Run quarterly reviews")
    args = parser.parse_args()
    run_backtest(args.ticker, args.start_date, args.end_date, args.quarterly_review)