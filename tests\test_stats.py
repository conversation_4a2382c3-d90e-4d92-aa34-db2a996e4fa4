import pytest
import pandas as pd
from src.stats import calculate_advanced_risk_stats, calculate_signal_stats

def test_calculate_advanced_risk_stats():
    """Test calculation of advanced risk metrics."""
    data = pd.DataFrame({
        "SPY_Return": [0.01] * 100,
        "JEPI_Return": [0.008] * 100
    }, index=pd.date_range("2020-01-01", periods=100))
    backtest_results = {
        "spy_timing_vix18_last_day_pnl5": pd.DataFrame({
            "Date": pd.date_range("2020-01-01", periods=100),
            "Total_Return": [0.01] * 100,
            "Fund_Cumulative": [100 * (1.01)**i for i in range(100)]
        })
    }
    stats = calculate_advanced_risk_stats(data, backtest_results, "SPY")
    assert not stats.empty
    assert all(col in stats.columns for col in ["Sharpe Ratio", "Sortino Ratio", "Max Drawdown (%)", "Alpha vs JEPI (%)", "Information Ratio", "<PERSON> Alpha (%)"])

def test_calculate_signal_stats():
    """Test calculation of signal statistics."""
    data = pd.DataFrame({
        "SPY_Return": [0.01] * 100
    }, index=pd.date_range("2020-01-01", periods=100))
    signals = pd.DataFrame({
        "voting_signal": [1] * 100,
        "vix_signal": [1] * 100,
        "ema_signal": [1] * 100,
        "mom_signal": [1] * 100,
        "consecutive_triggers_3": [3] * 100,
        "consecutive_triggers_5": [5] * 100,
        "signal_flips": [2] * 100
    }, index=pd.date_range("2020-01-01", periods=100))
    stats = calculate_signal_stats(data, signals, "SPY")
    assert not stats.empty
    assert all(col in stats.columns for col in ["True Positives", "False Positives", "Accuracy", "Signal Flips Mean", "Information Coefficient"])