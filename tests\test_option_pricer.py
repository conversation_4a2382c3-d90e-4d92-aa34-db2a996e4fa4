import pytest
import pandas as pd
from src.option_pricer import estimate_option_prices

def test_estimate_option_prices():
    """Test option price estimation with sample data."""
    data = pd.DataFrame({
        "SPY_Price": [100.0] * 5,
        "VIX": [20.0] * 5
    }, index=pd.date_range("2020-01-01", periods=5))
    priced_data = estimate_option_prices(data, "SPY")
    assert "call_price" in priced_data.columns
    assert "put_price" in priced_data.columns
    assert priced_data["call_price"].iloc[0] > 0
    assert priced_data["put_price"].iloc[0] > 0